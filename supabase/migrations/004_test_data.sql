-- Insert test data for development
-- This file creates sample data to test the application

-- Insert test users (these would normally be created via auth, but for testing we'll insert directly)
INSERT INTO users (id, email, name, role, status, avatar_url, phone, location, verified) VALUES
('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '<PERSON>', 'customer', 'active', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150', '******-0101', 'San Francisco, CA', true),
('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '<PERSON>', 'pro', 'active', 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150', '******-0102', 'San Francisco, CA', true),
('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', '<PERSON>', 'pro', 'active', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150', '******-0103', 'Oakland, CA', true),
('550e8400-e29b-41d4-a716-446655440004', '<EMAIL>', 'Sarah <PERSON>', 'customer', 'active', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150', '******-0104', 'Berkeley, CA', true);

-- Insert test contractors
INSERT INTO contractors (id, user_id, business_name, license, specialties, tier, status, rating_average, rating_count) VALUES
('650e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', 'Wilson Kitchen & Bath', 'LIC123456', ARRAY['kitchen', 'bathroom'], 'premium', 'active', 4.8, 24),
('650e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003', 'Johnson General Contracting', 'LIC789012', ARRAY['kitchen', 'bathroom', 'flooring', 'painting'], 'elite', 'active', 4.9, 31);

-- Insert test projects
INSERT INTO projects (id, title, description, category, status, customer_id, budget, timeline, location) VALUES
('750e8400-e29b-41d4-a716-446655440001', 'Modern Kitchen Renovation', 'Complete kitchen remodel with new cabinets, countertops, and appliances', 'kitchen', 'active', '550e8400-e29b-41d4-a716-446655440001', 
 '{"min": 15000, "max": 25000, "currency": "USD", "flexible": true}', 
 '{"duration": 21, "flexible": true, "startDate": "2024-02-01"}',
 '{"address": "123 Main St", "city": "San Francisco", "state": "CA", "zipCode": "94102"}'),
('750e8400-e29b-41d4-a716-446655440002', 'Bathroom Remodel', 'Master bathroom renovation with walk-in shower and double vanity', 'bathroom', 'active', '550e8400-e29b-41d4-a716-446655440004',
 '{"min": 8000, "max": 15000, "currency": "USD", "flexible": false}',
 '{"duration": 14, "flexible": false, "startDate": "2024-02-15"}',
 '{"address": "456 Oak Ave", "city": "Berkeley", "state": "CA", "zipCode": "94704"}');

-- Insert test bids
INSERT INTO bids (id, project_id, contractor_id, status, amount, timeline, description, terms, expires_at) VALUES
('850e8400-e29b-41d4-a716-446655440001', '750e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440001', 'submitted', 22000.00,
 '{"startDate": "2024-02-05", "endDate": "2024-02-26", "duration": 21}',
 'Complete kitchen renovation including demolition, electrical, plumbing, and installation of new fixtures.',
 'Payment terms: 30% upfront, 40% at midpoint, 30% on completion. All materials included.',
 '2024-02-10 23:59:59+00'),
('850e8400-e29b-41d4-a716-446655440002', '750e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440002', 'submitted', 19500.00,
 '{"startDate": "2024-02-01", "endDate": "2024-02-22", "duration": 21}',
 'Full kitchen remodel with premium finishes and energy-efficient appliances.',
 'Payment terms: 25% upfront, 50% at midpoint, 25% on completion. 2-year warranty included.',
 '2024-02-10 23:59:59+00');

-- Insert test reviews
INSERT INTO reviews (id, project_id, reviewer_id, reviewee_id, rating, title, content, verified) VALUES
('950e8400-e29b-41d4-a716-446655440001', '750e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', 5, 'Excellent Kitchen Renovation', 'Jane and her team did an amazing job on our kitchen. Professional, on-time, and the quality is outstanding. Highly recommend!', true),
('950e8400-e29b-41d4-a716-446655440002', '750e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440003', 4, 'Great Bathroom Remodel', 'Mike did a solid job on our bathroom renovation. Good communication and fair pricing. Minor delays but overall satisfied.', true);

-- Insert test conversations
INSERT INTO conversations (id, project_id, participants) VALUES
('a50e8400-e29b-41d4-a716-446655440001', '750e8400-e29b-41d4-a716-446655440001', ARRAY['550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002']),
('a50e8400-e29b-41d4-a716-446655440002', '750e8400-e29b-41d4-a716-446655440002', ARRAY['550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440003']);

-- Insert test messages
INSERT INTO messages (id, conversation_id, sender_id, content) VALUES
('b50e8400-e29b-41d4-a716-446655440001', 'a50e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'Hi Jane, I''m interested in your bid for the kitchen renovation. Can we discuss the timeline?'),
('b50e8400-e29b-41d4-a716-446655440002', 'a50e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', 'Hi John! Absolutely, I''d be happy to discuss the timeline. When would be a good time for a call?'),
('b50e8400-e29b-41d4-a716-446655440003', 'a50e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440004', 'Hello Mike, your bathroom remodel proposal looks great. Do you have availability in February?'),
('b50e8400-e29b-41d4-a716-446655440004', 'a50e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003', 'Hi Sarah! Yes, I have availability starting February 15th. Let me know if that works for you.');

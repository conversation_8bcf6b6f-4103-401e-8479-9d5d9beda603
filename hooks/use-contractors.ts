"use client"

import { useState, useEffect, useCallback } from 'react'
import { contractorService } from '@/services/database'
import { useToastActions } from '@/components/ui/toast-system'
import type { Tables } from '@/lib/supabase'
import type { SearchFilters, SearchResult } from '@/types'

export interface UseContractorsOptions {
  autoFetch?: boolean
  initialFilters?: Partial<SearchFilters>
}

export interface UseContractorsReturn {
  contractors: any[]
  loading: boolean
  error: string | null
  total: number
  filters: SearchFilters
  searchContractors: (newFilters: Partial<SearchFilters>) => Promise<void>
  refetch: () => Promise<void>
  clearFilters: () => void
}

const defaultFilters: SearchFilters = {
  search: '',
  category: [],
  rating: 0,
  verified: undefined,
  location: '',
  sortBy: 'rating',
  sortOrder: 'desc',
  page: 1,
  limit: 12
}

export function useContractors(options: UseContractorsOptions = {}): UseContractorsReturn {
  const { autoFetch = true, initialFilters = {} } = options
  const { showError } = useToastActions()
  
  const [contractors, setContractors] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [total, setTotal] = useState(0)
  const [filters, setFilters] = useState<SearchFilters>({
    ...defaultFilters,
    ...initialFilters
  })

  const searchContractors = useCallback(async (newFilters: Partial<SearchFilters> = {}) => {
    setLoading(true)
    setError(null)

    const searchFilters = { ...filters, ...newFilters }
    setFilters(searchFilters)

    try {
      const response = await contractorService.searchContractors(searchFilters)

      if (response.success && response.data) {
        setContractors(response.data.items)
        setTotal(response.data.total)
      } else {
        setError(response.error || 'Failed to search contractors')
        showError(response.error || 'Failed to search contractors')
        setContractors([])
        setTotal(0)
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred while searching contractors'
      setError(errorMessage)
      showError(errorMessage)
      setContractors([])
      setTotal(0)
    } finally {
      setLoading(false)
    }
  }, [filters, showError])

  const refetch = useCallback(() => {
    return searchContractors({})
  }, [searchContractors])

  const clearFilters = useCallback(() => {
    const clearedFilters = { ...defaultFilters }
    setFilters(clearedFilters)
    searchContractors(clearedFilters)
  }, [searchContractors])

  useEffect(() => {
    if (autoFetch) {
      searchContractors()
    }
  }, []) // Only run on mount

  return {
    contractors,
    loading,
    error,
    total,
    filters,
    searchContractors,
    refetch,
    clearFilters
  }
}

// Hook for getting a single contractor with full details
export function useContractor(contractorId: string | null) {
  const [contractor, setContractor] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { error: showError } = useToast()

  const fetchContractor = useCallback(async () => {
    if (!contractorId) return

    setLoading(true)
    setError(null)

    try {
      const response = await contractorService.findWithUserData(contractorId)

      if (response.success && response.data) {
        setContractor(response.data)
      } else {
        setError(response.error || 'Failed to fetch contractor')
        showError(response.error || 'Failed to fetch contractor')
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred while fetching contractor'
      setError(errorMessage)
      showError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [contractorId, showError])

  useEffect(() => {
    fetchContractor()
  }, [fetchContractor])

  return {
    contractor,
    loading,
    error,
    refetch: fetchContractor
  }
}

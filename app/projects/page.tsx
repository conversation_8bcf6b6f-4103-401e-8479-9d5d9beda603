"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { CustomerRoute } from "@/components/route-guard"
import { RoleSwitcher } from "@/components/role-switcher"
import { useUser } from "@/contexts/user-context"
import { ProjectCard, EnhancedCard } from "@/components/ui/enhanced-card"
import { projectService } from "@/services/database"
import { Project } from "@/types"
import { Plus, Clock, CheckCircle, Hammer, AlertCircle, Loader2 } from "lucide-react"
import Link from "next/link"

export default function ProjectsPage() {
  const { user } = useUser()
  const router = useRouter()
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadProjects()
  }, [user])

  const loadProjects = async () => {
    if (!user) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)
      console.log('Loading projects for user:', user.id)
      const response = await projectService.findByCustomerId(user.id)
      console.log('Projects response:', response)

      if (response.success && response.data) {
        setProjects(response.data as any)
      } else {
        // If database is not set up yet, show a helpful message
        if (response.error?.includes('relation') || response.error?.includes('table')) {
          setError('Database not set up yet. Please run the database migrations.')
        } else {
          setError(response.error || 'Failed to load projects')
        }
        setProjects([])
      }
    } catch (err) {
      setError("Failed to load projects. Please try again.")
      console.error("Error loading projects:", err)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <Clock className="h-4 w-4 text-status-info" />
      case "completed":
        return <CheckCircle className="h-4 w-4 text-status-success" />
      default:
        return <Clock className="h-4 w-4 text-slate-400" />
    }
  }

  const formatBudget = (budget: any) => {
    if (!budget || typeof budget !== 'object' || !budget.min || !budget.max) {
      return 'Budget not specified'
    }
    return `$${budget.min.toLocaleString()} - $${budget.max.toLocaleString()}`
  }

  const formatLocation = (location: any) => {
    if (!location) return 'Location not specified'
    if (typeof location === 'string') return location
    if (typeof location === 'object' && location.address) return location.address
    if (typeof location === 'object' && location.city) return location.city
    return 'Location not specified'
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return "1 day ago"
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} week${Math.ceil(diffDays / 7) > 1 ? 's' : ''} ago`
    return `${Math.ceil(diffDays / 30)} month${Math.ceil(diffDays / 30) > 1 ? 's' : ''} ago`
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      <UnifiedNavigation />

      <div className="container-premium section-premium">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-12 gap-6">
          <div className="space-y-2">
            <h1 className="text-2xl lg:text-3xl font-bold text-slate-900">Your Projects</h1>
            <p className="text-lg text-slate-600 max-w-2xl">Manage and track your renovation projects from start to finish</p>
          </div>

          <Link href="/">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Project
            </Button>
          </Link>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <EnhancedCard key={i} loading={true} />
            ))}
          </div>
        )}

        {/* Error State */}
        {error && (
          <EnhancedCard className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">Error Loading Projects</h3>
            <p className="text-slate-500 mb-4">{error}</p>
            <Button onClick={loadProjects} className="bg-brand-primary hover:bg-brand-primary/90">
              <Loader2 className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </EnhancedCard>
        )}

        {/* Projects List */}
        {!loading && !error && (
          <div className="space-y-4">
            {projects.length === 0 ? (
              <EnhancedCard className="p-12 text-center">
                <Hammer className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-900 mb-2">No Projects Yet</h3>
                <p className="text-slate-500 mb-6">Start your first renovation project today</p>
                <Link href="/">
                  <Button className="bg-brand-primary hover:bg-brand-primary/90">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Project
                  </Button>
                </Link>
              </EnhancedCard>
            ) : (
              projects.map((project) => (
                <ProjectCard
                  key={project.id}
                  project={{
                    id: project.id,
                    title: project.title,
                    category: project.category,
                    status: project.status,
                    budget: formatBudget(project.budget),
                    location: formatLocation(project.location),
                    createdAt: formatDate(new Date(project.created_at)),
                    bidsCount: 0 // TODO: Get actual bid count from bids table
                  }}
                  variant="customer"
                  onView={(id) => router.push(`/project/${id}`)}
                  onEdit={(id) => router.push(`/project/${id}/edit`)}
                />
              ))
            )}
          </div>
        )}
      </div>

      <RoleSwitcher />
    </div>
  )
}

# Database Setup Guide

This guide explains how to set up the RenovHub database with all necessary data.

## Overview

The RenovHub application uses Supabase as its database backend. All hardcoded mock data has been removed from the frontend components and replaced with proper database integration.

## Quick Setup

To set up the database with all migrations and test data:

```bash
npm run db:setup
```

This will:
1. Run all database migrations
2. Set up Row Level Security policies
3. Seed the database with comprehensive test data

## Manual Setup

If you prefer to run each step manually:

### 1. Run Migrations

```bash
npm run db:migrate
# or
supabase db push
```

### 2. Seed Test Data

```bash
npm run db:seed
# or
npx tsx scripts/seed-database.ts
```

## Database Structure

The database includes the following main tables:

- **users** - Customer and contractor user accounts
- **contractors** - Contractor business profiles and details
- **projects** - Customer renovation projects
- **bids** - Contractor bids on projects
- **reviews** - Project reviews and ratings
- **conversations** - Message conversations between users
- **messages** - Individual messages in conversations

## Test Data

The seeding process creates realistic test data including:

- **20 users** (12 customers, 8 contractors)
- **10 contractor profiles** with specialties, ratings, and portfolios
- **15 projects** across different categories (kitchen, bathroom, flooring, etc.)
- **30+ bids** with detailed pricing and timelines
- **25+ reviews** with ratings and feedback
- **Multiple conversations** with realistic message exchanges

## Data Categories

### Contractors
- Kitchen & Bath specialists
- General contractors
- Flooring experts
- Painters
- Electricians
- Plumbers

### Projects
- Kitchen renovations
- Bathroom remodels
- Flooring installations
- Interior painting
- Electrical upgrades
- Various other home improvement projects

### Locations
Test data covers the San Francisco Bay Area including:
- San Francisco
- Oakland
- Berkeley
- San Jose
- Palo Alto
- Fremont
- San Mateo

## Frontend Integration

All frontend components now use database services instead of hardcoded data:

- **Contractors page** - Uses `useContractors` hook with `contractorService`
- **Projects pages** - Use `projectService` for CRUD operations
- **Bids pages** - Use `biddingService` for bid management
- **Search functionality** - Integrated with database search
- **Dashboard** - Real-time data from database

## Environment Setup

Make sure your `.env.local` file contains the correct Supabase configuration:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Troubleshooting

### Migration Issues
If migrations fail:
1. Check your Supabase connection
2. Ensure you have the correct permissions
3. Try running `supabase db reset` to start fresh

### Seeding Issues
If seeding fails:
1. Check that migrations have run successfully
2. Verify your database connection
3. Check the console for specific error messages

### No Data Showing
If the frontend shows no data:
1. Verify the database has been seeded
2. Check browser console for API errors
3. Ensure your environment variables are correct

## Development Workflow

1. **Initial setup**: Run `npm run db:setup`
2. **Add new features**: Create migrations for schema changes
3. **Update test data**: Modify the seeding scripts as needed
4. **Reset database**: Use `supabase db reset` followed by `npm run db:setup`

## Production Considerations

- The test data is only for development
- Production databases should use real user data
- Consider data privacy and security requirements
- Implement proper backup and recovery procedures

## Next Steps

With the database properly set up:
1. Start the development server: `npm run dev`
2. Test all functionality with real database data
3. Create additional test scenarios as needed
4. Implement any missing database operations
